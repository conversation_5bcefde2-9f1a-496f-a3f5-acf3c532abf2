<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoporrevisar.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/paisoculto.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/general/preparar.php';

$sum_riesgo                   = 0;
$n_fila                       = 0;
$tabselected                  = 1;
$partidos_porrevisar_bytorneo = array();
$ajax                         = 0;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		$method_get = 1;
		
		if (isset($_GET['i'])) {
			$success_display = 'show';
			$success_text    = 'El partido ha sido ingresado.';
		}
		if (isset($_GET['m'])) {
			$success_display = 'show';
			$success_text    = 'El partido ha sido modificado.';
		}
		if (isset($_GET['d'])) {
			$success_display = 'show';
			$success_text    = 'El partido ha sido eliminado.';
		}
		if (isset($_GET['nppr'])) {
			$success_display = 'show';
			$success_text    = 'No hay partidos por revisar.';
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$ajax = $_POST['ajax'] ?? 0;
		
		if ($ajax == 0) {
			$tabselected = limpiar_datos($_POST["tabselected"]);
		}
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion postsolo
#region sub_editpartidoporrevisar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editpartidoporrevisar'])) {
	try {
		$_SESSION['idpartidoporrevisar'] = limpiar_datos($_POST['selidpartidoporrevisar']);
		
		header('Location: epartidoporrevisar');
		exit();
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_editpartidoporrevisar
#region sub_addpartido
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addpartido'])) {
	try {
		$method_addpartido = 1;
		
		$_SESSION['idpartidoporrevisar'] = limpiar_datos($_POST['selidpartidoporrevisar']);
		
		header('Location: ipartido');
		exit();
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_addpartido
#region sub_delpartidosporrevisar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpartidosporrevisar'])) {
	try {
		$method_sub_delpartidosporrevisar = 1;
		
		PartidoPorRevisar::deleteAll($conexion);
		
		$success_display = 'show';
		$success_text    = 'Todos los partidos por revisar han sido eliminados.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_delpartidosporrevisar
#region sub_subir
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_subir'])) {
	try {
		$method_sub_subir = 1;
		
		$conexion->beginTransaction();
		
		$archivo    = $_FILES['archivocsv']['tmp_name'];
		$archivocsv = file($archivo);
		
		PartidoPorRevisar::uploadCSV($archivocsv, $conexion);
		
		$conexion->commit();
		
		$success_display = 'show';
		$success_text    = 'Los partidos han sido subidos.';
		
	} catch (Exception $e) {
		$conexion->rollback();
		
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_subir
#region sub_cargar_api
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_cargar_api'])) {
	try {
		$method_sub_cargar_api = 1;

		$fecha_api = limpiar_datos($_POST['fecha_api']);

		// Validate date format
		if (empty($fecha_api)) {
			throw new Exception('Debe seleccionar una fecha');
		}

		if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_api)) {
			throw new Exception('Formato de fecha inválido. Use yyyy-mm-dd');
		}

		$conexion->beginTransaction();

		// Load matches from API
		$recordsCreated = PartidoPorRevisar::loadFromFootyApi($fecha_api, $conexion);

		$conexion->commit();

		$success_display = 'show';
		$success_text    = "Se han cargado {$recordsCreated} partidos desde la API para la fecha {$fecha_api}.";

	} catch (Exception $e) {
		$conexion->rollback();

		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_cargar_api
#region sub_eliminar_partidosporrevisar_hasta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_eliminar_partidosporrevisar_hasta'])) {
	try {
		$hora_hasta = limpiar_datos($_POST['eliminar_partidosporrevisar_hasta_hora']);
		
		PartidoPorRevisar::delete_hasta_hora($hora_hasta, $conexion);
		
		$success_display = 'show';
		$success_text    = 'Partidos eliminados.';
	
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_eliminar_partidosporrevisar_hasta
#region sub_convertir_todos
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_convertir_todos'])) {
	try {
		$method_sub_convertir_todos = 1;

		// Check if this is an AJAX request
		if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
			$conexion->beginTransaction();

			// Get all PartidoPorRevisar records that are ready to be converted
			$param = array();
			$param['solotoplay'] = 1;
			$partidosporrevisar = PartidoPorRevisar::getList($param, $conexion);

			$convertedCount = 0;
			$errors = array();

			foreach ($partidosporrevisar as $partidoporrevisar) {
				try {
					// Create new Partido object
					$newpartido = new Partido();

					// Map fields from PartidoPorRevisar to Partido (following ipartido.php logic)
					$newpartido->pais        = $partidoporrevisar->pais;
					$newpartido->fecha       = $partidoporrevisar->fecha;
					$newpartido->horamilitar = $partidoporrevisar->horamilitar;
					$newpartido->formhome    = $partidoporrevisar->formhome;
					$newpartido->formaway    = $partidoporrevisar->formaway;
					$newpartido->home_xg     = $partidoporrevisar->home_xg;
					$newpartido->away_xg     = $partidoporrevisar->away_xg;
					$newpartido->home        = $partidoporrevisar->equipo_home;
					$newpartido->away        = $partidoporrevisar->equipo_away;

					// Set additional required fields
					$newpartido->hora = $partidoporrevisar->hora;
					$newpartido->diasemana = $partidoporrevisar->diasemana;

					// Get pais_torneo ID
					$idpais = Pais::getByNombre($newpartido->pais, $conexion);
					$newpartido->pais_torneo->id = $idpais;

					// Add the new Partido
					$newpartido->add($conexion);

					// Mark PartidoPorRevisar as done
					$modpartidoporrevisar = new PartidoPorRevisar();
					$modpartidoporrevisar->id = $partidoporrevisar->id;
					$modpartidoporrevisar->modifyDone($conexion);

					$convertedCount++;

				} catch (Exception $e) {
					$errors[] = "Error converting match {$partidoporrevisar->matchup}: " . $e->getMessage();
				}
			}

			$conexion->commit();

			// Return JSON response
			header('Content-Type: application/json');
			if (empty($errors)) {
				echo json_encode([
					'success' => true,
					'message' => "Se han convertido {$convertedCount} partidos exitosamente.",
					'converted_count' => $convertedCount
				]);
			} else {
				echo json_encode([
					'success' => false,
					'message' => "Se convirtieron {$convertedCount} partidos, pero hubo errores: " . implode('; ', $errors),
					'converted_count' => $convertedCount,
					'errors' => $errors
				]);
			}
			exit();
		}

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
			header('Content-Type: application/json');
			echo json_encode([
				'success' => false,
				'message' => $e->getMessage()
			]);
			exit();
		} else {
			$error_display = 'show';
			$error_text = $e->getMessage();
		}
	}
}
#endregion sub_convertir_todos
#region try
try {
	if ($ajax == 0) {
	
	
	} else {
		#region region AJAX ocultar_pais
		if (isset($_POST['ajax_ocultar_pais']) && $_POST['ajax_ocultar_pais'] == 1) {
			try {
				$sel_partido_porrevisar_pais = htmlspecialchars($_POST['partido_porrevisar_pais']);
				
				$newpaisoculto         = new PaisOculto;
				$newpaisoculto->nombre = $sel_partido_porrevisar_pais;
				$newpaisoculto->add($conexion);
				
				// Return JSON response
				$success = true;
				
				header('Content-Type: application/json');
				echo json_encode([
					                 'success' => $success,
					                 'message' => $success ? 'Action completed' : 'Something went wrong'
				                 ]);
				exit();
				
			} catch (Exception $e) {
				$success = false;
				
				header('Content-Type: application/json');
				echo json_encode([
					                 'success' => $success,
					                 'message' => $success ? 'Action completed' : 'Something went wrong'
				                 ]);
				exit();
			}
		}
		#endregion AJAX ocultar_pais
		#region region AJAX eliminar_pais
		if (isset($_POST['ajax_eliminar_pais']) && $_POST['ajax_eliminar_pais'] == 1) {
			try {
				$sel_partido_porrevisar_pais = htmlspecialchars($_POST['partido_porrevisar_pais']);
				
				PartidoPorRevisar::delete_all_same_torneo($sel_partido_porrevisar_pais, $conexion);
				
				// Return JSON response
				$success = true;
				
				header('Content-Type: application/json');
				echo json_encode([
					                 'success' => $success,
					                 'message' => $success ? 'Action completed' : 'Something went wrong'
				                 ]);
				exit();
				
			} catch (Exception $e) {
				$success = false;
				
				header('Content-Type: application/json');
				echo json_encode([
					                 'success' => $success,
					                 'message' => $success ? 'Action completed' : 'Something went wrong'
				                 ]);
				exit();
			}
		}
		#endregion AJAX eliminar_pais
	}
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

if ($ajax == 0) {
	require_once __ROOT__ . '/views/lpartidosporrevisar.view.php';
}

?>

