<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/paisseason.php';
require_once __ROOT__ . '/src/general/preparar.php';

$partidosinfo         = array();
$tabselected          = 1;
$nom_pais_search      = '';
$torneos_grouped      = array();
$home_search          = '';
$away_search          = '';
$perc_progress_upload = '';
$season_upload        = '';
$id_pais_upload       = '';
$season_search        = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		$method_get = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$method_postsolo = 1;
		
		$tabselected             = limpiar_datos($_POST["tabselected"]);
		$nom_pais_search         = limpiar_datos($_POST['pais_search']);
		$season_search           = limpiar_datos($_POST['season_search']);
		$pais_search_grouped     = limpiar_datos($_POST['pais_search_grouped']);
		$id_pais_upload          = limpiar_datos($_POST['pais_upload']);
		$season_upload           = limpiar_datos($_POST['season_upload']);
		$pais_modmasiv           = limpiar_datos($_POST['pais_modmasiv']);
		$team_acorregir_modmasiv = limpiar_datos($_POST['team_acorregir_modmasiv']);
		$team_corregido_modmasiv = limpiar_datos($_POST['team_corregido_modmasiv']);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion postsolo
#region sub_upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_upload'])) {
	try {
		$method_sub_upload = 1;
		
		$conexion->beginTransaction();
		
		$archivo    = $_FILES['archivocsv']['tmp_name'];
		$archivocsv = file($archivo);
		
		$param               = array();
		$param['archivocsv'] = $archivocsv;
		$param['season']     = $season_upload;
		$param['idpais']     = $id_pais_upload;
		PartidoInfo::deleteSelected($param, $conexion);
		PartidoInfo::uploadCSV($param, $conexion);
		
		$nom_pais_upload = Pais::get($id_pais_upload, $conexion)->nombre;
		
		$param             = array();
		$param['nom_pais'] = $nom_pais_upload;
		$param['season']   = $season_upload;
		PartidoInfo::add_pais_season($param, $conexion);
		
		$conexion->commit();
		
		$perc_progress_upload = '';
		
		$success_display = 'show';
		$success_text    = 'El archivo fue cargado.';
		
		$nom_pais_search = $nom_pais_upload;
		$season_search   = $season_upload;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_upload
#region sub_delinfo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delinfo'])) {
	try {
		$method_sub_delinfo = 1;
		
		$param           = array();
		$param['season'] = $season_search;
		$param['idpais'] = Pais::getByNombre($nom_pais_search, $conexion);
		PartidoInfo::deleteSelected($param, $conexion);
		
		$success_display = 'show';
		$success_text    = 'Los registros han sido eliminados.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_delinfo
#region sub_modmasiv
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modmasiv'])) {
	try {
		$conexion->beginTransaction();
		
		$param                   = array();
		$param['pais']           = $pais_modmasiv;
		$param['team_acorregir'] = $team_acorregir_modmasiv;
		$param['team_corregido'] = $team_corregido_modmasiv;
		PartidoInfo::modify_corregir_team($param, $conexion);
		
		$conexion->commit();
		
		$success_display = 'show';
		$success_text    = 'La modificacion ha sido realizada.';
		
	} catch (Exception $e) {
		$conexion->rollback();
		
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_modmasiv
#region try
try {
	$method_try = 1;
	
	$paises = Pais::getList($conexion);
	
	if (!empty($nom_pais_search)) {
		$param                  = array();
		$param['season']        = $season_search;
		$param['pais']          = $nom_pais_search;
		$param['home_search']   = $home_search;
		$param['away_search']   = $away_search;
		$param['orderby_fecha'] = 1;
		$partidosinfo           = PartidoInfo::getList($param, $conexion);
	}
	
	if (!empty($pais_search_grouped)) {
		$param             = array();
		$param['nom_pais'] = $pais_search_grouped;
		$torneos_grouped   = PartidoInfo::get_list_torneos_grouped($param, $conexion);
	}
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/ipartidoinfo.view.php';

?>





