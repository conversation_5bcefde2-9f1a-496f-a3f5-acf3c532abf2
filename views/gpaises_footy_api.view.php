<?php
#region region DOCS
/** @var PaisesTorneoFootyApi $newPaisesTorneoFootyApi */
/** @var PaisesTorneoFootyApi[] $paisesTorneoFootyApiList */
/** @var Pais[] $paises */

use App\classes\PaisesTorneoFootyApi;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Gestionar Países Footy API</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <h1 class="page-header">Gestionar Países Footy API</h1>

        <hr>
        <!-- END page-header -->

        <?php #region region Main Form Structure (for table and hidden inputs) ?>
        <form action="gestionar-paises-footy-api" method="POST" id="mainForm">
            <input type="hidden" id="mdl_delete_id" name="mdl_delete_id">

            <?php #region region SUBMIT sub_delete ?>
            <div class="col" style="display: none">
                <button type="submit" form="mainForm" id="sub_delete" name="sub_delete" class="btn btn-danger w-100">
                    sub_delete
                </button>
            </div>
            <?php #endregion sub_delete ?>
            
            <!-- Button to trigger Add modal -->
            <div class="row mt-3 mb-3">
                <div class="col-md-12">
                    <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#addModal">
                        <i class="fa fa-plus me-1"></i> Nuevo Registro
                    </button>
                </div>
            </div>

            <!-- Search Filter Section -->
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <span>Filtros de Búsqueda</span>
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-5">
                            <label for="searchCountryName" class="form-label">Buscar por País:</label>
                            <input type="text" id="searchCountryName" class="form-control" placeholder="Ej: premier, england, canada..." />
                        </div>
                        <div class="col-md-4">
                            <label for="searchFootyId" class="form-label">Buscar por ID Footy:</label>
                            <input type="number" id="searchFootyId" class="form-control" placeholder="Ej: 123" />
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" id="clearFilters" class="btn btn-secondary w-100">
                                <i class="fa fa-times me-1"></i> Limpiar Filtros
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <?php #region region PANEL records ?>
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <span>Registros de Países Footy API</span>
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="p-1 table-nowrap" style="overflow: auto">
                    <?php #region region TABLE records ?>
                    <table class="table table-sm" id="recordsTable">
                        <thead>
                        <tr>
                            <th class="w-70px">Acciones</th>
                            <th class="text-start">País</th>
                            <th class="text-center">Temporada</th>
                            <th class="text-center">ID Footy</th>
                        </tr>
                        </thead>
                        <tbody class="fs-13px" id="recordsTableBody">
                        <?php #region region ARRAY records ?>
                        <?php foreach ($paisesTorneoFootyApiList as $record): ?>
                            <tr class="table-row" data-country-name="<?php echo htmlspecialchars(strtolower($record->getPaisNombre() ?? '')); ?>" data-footy-id="<?php echo $record->getIdFooty(); ?>">
                                <td class="align-middle">
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#mdl_delete" data-id="<?php echo limpiar_datos($record->getId()); ?>" title="Eliminar registro">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                                <td class="align-middle text-start">
                                    <?php echo htmlspecialchars($record->getPaisNombre() ?? 'País no encontrado'); ?>
                                </td>
                                <td class="align-middle text-center">
                                    <?php echo htmlspecialchars($record->getSeason()); ?>
                                </td>
                                <td class="align-middle text-center">
                                    <?php echo $record->getIdFooty(); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <?php #endregion array records ?>
                        </tbody>
                    </table>

                    <!-- No results message (initially hidden) -->
                    <div id="noResultsMessage" class="text-center py-4" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle me-2"></i>
                            No se encontraron resultados que coincidan con los filtros aplicados.
                        </div>
                    </div>
                    <?php #endregion table records ?>
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel records ?>

            <?php #region region MODAL mdl_delete ?>
            <div class="modal fade" id="mdl_delete">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar registro</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>¿Está seguro que desea eliminar este registro?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" form="mainForm" id="btn_delete" name="sub_delete" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion mdl_delete ?>
        </form>
        <?php #endregion Main Form Structure ?>

        <!-- Add Modal -->
        <div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <form action="gestionar-paises-footy-api" method="POST" id="addFormInModal">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addModalLabel">Agregar Nuevo Registro</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- País -->
                            <div class="mb-3">
                                <label for="modal_id_pais" class="form-label">País: <span class="text-danger">*</span></label>
                                <select name="id_pais" id="modal_id_pais" class="form-select" required>
                                    <option value="">-- Seleccione País --</option>
                                    <?php foreach ($paises as $pais): ?>
                                        <option value="<?php echo ordena($pais->id); ?>" <?php echo (htmlspecialchars(@recover_var($newPaisesTorneoFootyApi->getIdPais() ?? '')) == ordena($pais->id)) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($pais->nombre); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <!-- Temporada -->
                            <div class="mb-3">
                                <label for="modal_season" class="form-label">Temporada: <span class="text-danger">*</span></label>
                                <input type="text" name="season" id="modal_season" value="<?php echo htmlspecialchars(@recover_var($newPaisesTorneoFootyApi->getSeason() ?? '')); ?>" class="form-control" required onclick="this.focus();this.select('')" />
                            </div>
                            <!-- ID Footy -->
                            <div class="mb-3">
                                <label for="modal_id_footy" class="form-label">ID Footy: <span class="text-danger">*</span></label>
                                <input type="number" name="id_footy" id="modal_id_footy" value="<?php echo htmlspecialchars(@recover_var($newPaisesTorneoFootyApi->getIdFooty() ?? '')); ?>" class="form-control" required onclick="this.focus();this.select('')" />
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                            <button type="submit" name="sub_add" class="btn btn-success">Agregar Registro</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        const addModal = document.getElementById('addModal');

        if (addModal) {
            addModal.addEventListener('shown.bs.modal', function () {
                document.getElementById('modal_id_pais').focus(); // Autofocus on the first field
            });
        }

        // Initialize search functionality
        initializeSearchFilters();
    });

    // Search and filter functionality
    function initializeSearchFilters() {
        const searchCountryName = document.getElementById('searchCountryName');
        const searchFootyId = document.getElementById('searchFootyId');
        const clearFiltersBtn = document.getElementById('clearFilters');
        const tableRows = document.querySelectorAll('.table-row');
        const noResultsMessage = document.getElementById('noResultsMessage');
        const recordsTable = document.getElementById('recordsTable');

        // Real-time search for country name
        searchCountryName.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            filterTable();
        });

        // Real-time search for Footy ID
        searchFootyId.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            filterTable();
        });

        // Clear filters button
        clearFiltersBtn.addEventListener('click', function() {
            searchCountryName.value = '';
            searchFootyId.value = '';
            filterTable();
            searchCountryName.focus();
        });

        function filterTable() {
            const countrySearchTerm = searchCountryName.value.toLowerCase().trim();
            const footyIdSearchTerm = searchFootyId.value.trim();
            let visibleRowsCount = 0;

            tableRows.forEach(function(row) {
                let showRow = true;

                // Filter by country name (approximate/fuzzy matching)
                if (countrySearchTerm.length >= 2) {
                    const countryName = row.getAttribute('data-country-name');
                    if (!countryName.includes(countrySearchTerm)) {
                        showRow = false;
                    }
                }

                // Filter by Footy ID (exact match)
                if (footyIdSearchTerm.length > 0 && showRow) {
                    const footyId = row.getAttribute('data-footy-id');
                    if (footyId !== footyIdSearchTerm) {
                        showRow = false;
                    }
                }

                // Show/hide row
                if (showRow) {
                    row.style.display = '';
                    visibleRowsCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Show/hide "no results" message
            if (visibleRowsCount === 0 && (countrySearchTerm.length >= 2 || footyIdSearchTerm.length > 0)) {
                recordsTable.style.display = 'none';
                noResultsMessage.style.display = 'block';
            } else {
                recordsTable.style.display = '';
                noResultsMessage.style.display = 'none';
            }
        }
    }
</script>

<script type="text/javascript">
    $('#mdl_delete').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_id = button.data('id');

        const mdl_delete_id = document.getElementById('mdl_delete_id');
        mdl_delete_id.value = recipient_id;
    })
</script>
<?php #endregion js ?>

</body>
</html>
